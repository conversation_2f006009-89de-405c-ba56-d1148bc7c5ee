'use client'

import {
  Card,
  Typography,
  Descriptions,
  Table,
  Tag,
  Button,
  Alert,
  Space,
  Divider,
  Timeline,
} from 'antd'
import {
  CheckCircleOutlined,
  DatabaseOutlined,
  LinkOutlined,
  FileTextOutlined,
  RocketOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography

interface ConfigReviewStepProps {
  formData: {
    formId: string
    formName: string
    sampleJson: any
    fieldMapping: Record<string, any>
  }
  onSubmit: () => void
  loading: boolean
  isEditMode?: boolean
}

interface MappingField {
  key: string
  name: string
  type: string
  required: boolean
  description: string
}

export function ConfigReviewStep({ formData, onSubmit, loading, isEditMode = false }: ConfigReviewStepProps) {
  const { formId, formName, sampleJson, fieldMapping } = formData

  // 转换字段映射为表格数据
  const mappingFields: MappingField[] = Object.entries(fieldMapping).map(([key, config]: [string, any]) => ({
    key,
    name: config.name,
    type: config.type,
    required: config.required,
    description: config.description || '-',
  }))

  const webhookUrl = `${window.location.origin}/api/webhook/${formId}`
  const tableName = `form_data_${formId}`

  const renderTypeTag = (type: string) => {
    const colorMap: Record<string, string> = {
      string: 'blue',
      number: 'green',
      boolean: 'orange',
      date: 'purple',
      array: 'cyan',
      object: 'magenta',
      text: 'volcano',
    }
    return <Tag color={colorMap[type] || 'default'}>{type}</Tag>
  }

  const columns: ColumnsType<MappingField> = [
    {
      title: '原始字段',
      dataIndex: 'key',
      width: 120,
      render: (key) => <Text code>{key}</Text>,
    },
    {
      title: '显示名称',
      dataIndex: 'name',
      width: 120,
    },
    {
      title: '数据类型',
      dataIndex: 'type',
      width: 100,
      render: renderTypeTag,
    },
    {
      title: '必填',
      dataIndex: 'required',
      width: 80,
      render: (required) => (
        <Tag color={required ? 'red' : 'default'}>
          {required ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
    },
  ]

  return (
    <div className="space-y-6">
      <Card>
        <Title level={4} className="mb-4">
          <CheckCircleOutlined className="mr-2" />
          确认配置信息
        </Title>

        <Alert
          message={isEditMode ? "配置修改确认" : "配置检查"}
          description={isEditMode
            ? "请仔细检查以下修改内容，确认无误后点击'更新配置'按钮保存修改。"
            : "请仔细检查以下配置信息，确认无误后点击'创建配置'按钮完成表单配置。"
          }
          type="info"
          showIcon
          className="mb-6"
        />

        {/* 基本信息 */}
        <div className="mb-6">
          <Title level={5} className="mb-3">
            <FileTextOutlined className="mr-2" />
            基本信息
          </Title>
          <Descriptions bordered size="small" column={2}>
            <Descriptions.Item label="表单ID" span={1}>
              <Text code>{formId}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="表单名称" span={1}>
              {formName}
            </Descriptions.Item>
            <Descriptions.Item label="数据表名" span={1}>
              <Text code>{tableName}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="字段数量" span={1}>
              <Text type="success">{mappingFields.length} 个</Text>
            </Descriptions.Item>
          </Descriptions>
        </div>

        {/* Webhook信息 */}
        <div className="mb-6">
          <Title level={5} className="mb-3">
            <LinkOutlined className="mr-2" />
            Webhook 配置
          </Title>
          <Descriptions bordered size="small" column={1}>
            <Descriptions.Item label="Webhook URL">
              <Text code copyable>{webhookUrl}</Text>
            </Descriptions.Item>
          </Descriptions>
          <Alert
            message="配置说明"
            description={
              <div>
                配置完成后，请在金数据表单设置中将上述URL设置为Webhook推送地址。
                系统将自动接收并处理推送的数据。
              </div>
            }
            type="warning"
            showIcon
            className="mt-3"
          />
        </div>

        {/* 字段映射 */}
        <div className="mb-6">
          <Title level={5} className="mb-3">
            <DatabaseOutlined className="mr-2" />
            字段映射配置
          </Title>
          <Table
            columns={columns}
            dataSource={mappingFields}
            rowKey="key"
            pagination={false}
            size="small"
            scroll={{ x: 600 }}
          />
        </div>

        {/* 执行计划 */}
        <div className="mb-6">
          <Title level={5} className="mb-3">
            <RocketOutlined className="mr-2" />
            执行计划
          </Title>
          <Timeline
            size="small"
            items={[
              {
                color: 'blue',
                children: (
                  <div>
                    <Text strong>保存表单配置</Text>
                    <div className="text-gray-500 text-sm mt-1">
                      将配置信息保存到数据库
                    </div>
                  </div>
                ),
              },
              {
                color: 'green',
                children: (
                  <div>
                    <Text strong>创建数据表</Text>
                    <div className="text-gray-500 text-sm mt-1">
                      根据字段映射创建数据表 <Text code>{tableName}</Text>
                    </div>
                  </div>
                ),
              },
              {
                color: 'orange',
                children: (
                  <div>
                    <Text strong>激活Webhook接收</Text>
                    <div className="text-gray-500 text-sm mt-1">
                      启用Webhook端点，开始接收数据
                    </div>
                  </div>
                ),
              },
              {
                color: 'purple',
                children: (
                  <div>
                    <Text strong>配置完成</Text>
                    <div className="text-gray-500 text-sm mt-1">
                      系统准备就绪，可以开始接收表单数据
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </div>

        <Divider />

        <div className="flex justify-center">
          <Button
            type="primary"
            size="large"
            onClick={onSubmit}
            loading={loading}
            icon={<CheckCircleOutlined />}
          >
            {loading
              ? (isEditMode ? '正在更新配置...' : '正在创建配置...')
              : (isEditMode ? '确认更新配置' : '确认创建配置')
            }
          </Button>
        </div>
      </Card>
    </div>
  )
}