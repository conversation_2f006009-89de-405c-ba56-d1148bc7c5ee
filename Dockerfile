# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine AS base

# 安装yarn
RUN npm install -g yarn

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY package*.json ./
COPY yarn.lock* ./
COPY prisma ./prisma/

# 安装依赖（仅生产依赖）
FROM base AS deps
RUN yarn install --production --frozen-lockfile

# 构建阶段
FROM base AS builder
RUN yarn install --frozen-lockfile
COPY . .

# 生成Prisma客户端
RUN npx prisma generate

# 构建应用
RUN yarn build

# 生产阶段
FROM node:18-alpine AS runner

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma
COPY --from=deps /app/node_modules ./node_modules

# 创建上传目录
RUN mkdir -p /app/public/uploads/avatars
RUN chown -R nextjs:nodejs /app/public/uploads

# 设置用户权限
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 启动应用
CMD ["node", "server.js"]