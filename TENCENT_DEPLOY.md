# 腾讯云5分钟部署指南 - 肺功能数据管理平台

## 前提条件
- 腾讯云CVM (2核4GB Ubuntu 20.04 或更高版本)
- 安全组开放3000端口和80端口
- 腾讯云轻量数据库已配置 (MySQL)
- 确保已有GitHub账号可访问项目代码

## 一键部署（推荐）
```bash
curl -fsSL https://raw.githubusercontent.com/your-repo/free_lung_function_project_admin/main/scripts/quick_deploy.sh | bash
```

## 手动部署步骤

### 1. 拉取git代码
```bash
git clone https://github.com/your-username/free_lung_function_project_admin.git
```

### 2. 文件夹授权
```bash
chmod -R 777 free_lung_function_project_admin
```

### 3. 腾讯云安装位置
```bash
cd /www/wwwroot/free_lung_function_project_admin
```

### 4. 配置腾讯云Docker镜像加速（官方配置）
```bash
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com"
  ]
}
EOF
```

### 5. 安装Docker和依赖
```bash
sudo apt update && sudo apt install -y docker.io docker-compose git curl
sudo systemctl restart docker
sudo systemctl enable docker
```

### 6. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要！）
nano .env
```

**关键环境变量配置**：
```bash
# 生产环境数据库 (使用内网地址获得更好性能)
DATABASE_URL="mysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"

# NextAuth 配置
NEXTAUTH_SECRET="your-secure-nextauth-secret-change-this"
NEXTAUTH_URL="http://your-server-ip:3000"

# JWT 密钥
JWT_SECRET="your-secure-jwt-secret-change-this"

# 应用配置
NODE_ENV="production"
PORT="3000"
```

### 7. 快速启动

#### 方式一：本地运行（推荐用于开发）
```bash
# 安装依赖
yarn install

# 生成Prisma客户端
npx prisma generate

# 启动开发服务器
yarn dev
```

#### 方式二：Docker容器运行
```bash
# 使用简化版Docker配置
docker-compose -f docker-compose.simple.yml up -d --build

# 或使用开发版Docker配置
docker-compose -f docker-compose.dev.yml up -d --build
```

#### 方式三：传统Docker配置
```bash
# 使用完整的docker-compose配置（包含MySQL和Redis）
docker-compose up -d --build
```

## 🚀 性能优化版本

### 超快版（网络很慢时推荐）
创建 `docker-compose.fast.yml`：
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.fast
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./public/uploads:/app/public/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

创建 `Dockerfile.fast`：
```dockerfile
# 使用腾讯云加速的Node.js镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 使用腾讯云NPM镜像加速
RUN yarn config set registry https://mirrors.tencent.com/npm/

# 复制package文件
COPY package*.json ./
COPY yarn.lock* ./

# 安装依赖
RUN yarn install --production --frozen-lockfile

# 复制应用代码
COPY . .

# 生成Prisma客户端
RUN npx prisma generate

# 构建应用
RUN yarn build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["yarn", "start"]
```

启动超快版：
```bash
docker-compose -f docker-compose.fast.yml up -d --build
```

## 🔄 服务器端重新部署教程

### ⚠️ 常见问题：Git冲突解决
如果遇到 `error: Your local changes would be overwritten by merge` 错误：

```bash
# 推荐解决方案：强制更新到最新版本
cd /www/wwwroot/free_lung_function_project_admin
git fetch origin main
git reset --hard origin/main
```

### 1. 拉取最新代码
```bash
git pull

# 如果出现文件冲突：强制覆盖本地修改（推荐）
git fetch origin main
git reset --hard origin/main
```

### 2. 重新构建并启动
```bash
# 停止现有容器
docker-compose down

# 重新构建并启动
docker-compose up -d --build

# 或使用快速版本
docker-compose -f docker-compose.fast.yml up -d --build
```

## 数据库初始化

### 自动数据库初始化
```bash
# 进入应用容器
docker exec -it free_lung_function_project_admin_app_1 /bin/sh

# 运行数据库迁移
npx prisma migrate deploy

# 生成Prisma客户端
npx prisma generate

# 运行数据库种子（可选）
npm run db:seed
```

### 验证数据库连接
```bash
# 检查数据库连接
docker exec -it free_lung_function_project_admin_app_1 npm run db:studio

# 或者运行健康检查
curl http://localhost:3000/api/health
```

## 常见错误排查

### 🚨 修复数据库连接错误
**问题**：无法连接到腾讯云数据库

**解决方案**：
```bash
cd /www/wwwroot/free_lung_function_project_admin

# 1. 检查环境变量配置
cat .env | grep DATABASE_URL

# 2. 确保使用正确的数据库URL
# 生产环境使用内网地址
echo 'DATABASE_URL="mysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"' >> .env

# 3. 重启容器
docker-compose restart app

# 4. 验证连接
docker exec -it free_lung_function_project_admin_app_1 npx prisma db push
```

### 🔧 修复端口占用问题
**问题**：端口3000已被占用

**解决方案**：
```bash
# 查找占用端口的进程
sudo lsof -i :3000

# 停止占用进程
sudo kill -9 [PID]

# 或者使用不同端口
echo 'PORT=3001' >> .env
docker-compose up -d --build
```

### 🛠️ 修复权限问题
**问题**：文件权限错误

**解决方案**：
```bash
# 修复文件权限
sudo chown -R $USER:$USER /www/wwwroot/free_lung_function_project_admin
chmod -R 755 /www/wwwroot/free_lung_function_project_admin

# 确保日志和上传目录可写
mkdir -p logs public/uploads
chmod -R 777 logs public/uploads
```

## 常用管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f app

# 重启服务
docker-compose restart app

# 进入容器调试
docker exec -it free_lung_function_project_admin_app_1 /bin/sh

# 强制重建镜像
docker-compose build --no-cache app

# 清理Docker缓存
docker system prune -a
```

## 验证部署成功

### 1. 健康检查
```bash
# 检查应用是否正常运行
curl http://localhost:3000/api/health

# 预期返回
{"status":"ok","database":"connected","timestamp":"2024-01-01T00:00:00.000Z"}
```

### 2. 访问系统
打开浏览器访问: `http://YOUR_SERVER_IP:3000`

**默认登录信息**：
- 需要通过数据库种子创建管理员账户
- 或通过注册页面创建第一个用户

### 3. 数据库验证
```bash
# 检查数据库表是否创建成功
docker exec -it free_lung_function_project_admin_app_1 npx prisma db push
```

## 生产环境优化

### 启用Nginx反向代理（推荐）
```bash
# 使用生产环境配置
docker-compose --profile production up -d
```

### 配置域名和SSL
1. 将域名解析指向服务器IP
2. 配置 `docker/nginx/nginx.conf`
3. 申请SSL证书并放置在 `docker/nginx/ssl/` 目录

### 性能监控
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -h
```

## 数据备份与恢复

### 备份数据
```bash
# 备份腾讯云数据库（推荐使用腾讯云控制台）
# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz public/uploads

# 备份日志
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs
```

### 恢复数据
```bash
# 恢复上传文件
tar -xzf uploads_backup_20240101.tar.gz

# 重启服务
docker-compose restart app
```

## 更新和维护

### 应用更新
```bash
# 1. 备份当前版本
git tag backup-$(date +%Y%m%d)

# 2. 拉取最新代码
git pull origin main

# 3. 重新部署
docker-compose down
docker-compose up -d --build

# 4. 运行数据库迁移
docker exec -it free_lung_function_project_admin_app_1 npx prisma migrate deploy
```

### 定期维护
```bash
# 清理Docker镜像和容器
docker system prune -f

# 查看日志大小并轮转
du -sh logs/
```

## 安全配置

### 1. 防火墙设置
```bash
# 只开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 3000  # 应用端口
sudo ufw allow 80    # HTTP (如果使用Nginx)
sudo ufw allow 443   # HTTPS (如果使用Nginx)
sudo ufw enable
```

### 2. 密钥管理
- 更改 `.env` 文件中的默认密钥
- 使用强密码生成器生成安全的密钥
- 定期轮换密钥

### 3. 数据库安全
- 使用腾讯云数据库的内网地址
- 配置数据库访问白名单
- 启用数据库审计日志

## 故障排除联系信息

如遇到部署问题，请按以下顺序排查：
1. 检查Docker服务状态：`sudo systemctl status docker`
2. 查看应用日志：`docker-compose logs -f app`
3. 验证环境变量：`cat .env`
4. 检查数据库连接：`docker exec -it app_container npx prisma db push`
5. 查看系统资源：`htop` 或 `docker stats`

**重要提醒**：
- 首次部署后请立即修改 `.env` 文件中的所有默认密钥
- 定期更新系统和Docker镜像
- 配置定期数据备份策略
- 监控应用运行状态和资源使用情况